"use client";

import styles from "./ImageGallery.module.scss";
import Image from "next/image";
import Button from "@/components/Buttons";

// Define interface for image data
interface ImageData {
  url: string;
  width: number;
  height: number;
}

// Define props interface for the component
interface ImageGalleryProps {
  images: ImageData[];
  onClick?: (event: React.MouseEvent<HTMLButtonElement>) => void;
}

const ImageGallery: React.FC<ImageGalleryProps> = ({ images, onClick }) => {
  const firstImage = images[0];
  const remainingImages = images.slice(1);

  // Function to determine image orientation
  const getOrientation = (image: ImageData): "portrait" | "landscape" => {
    return image.width < image.height ? "portrait" : "landscape";
  };

  // Function to swap landscape-portrait pairs within the first three images
  const swapImages = (images: ImageData[]): ImageData[] => {
    const result = [...images]; // Create a copy of the array
    const maxIndex = Math.min(2, images.length - 1); // Limit to first three images or less
    let swapped = true;
    while (swapped && maxIndex > 0) {
      swapped = false;
      for (let i = 0; i < maxIndex; i++) {
        const currentOrientation = getOrientation(result[i]);
        const nextOrientation = getOrientation(result[i + 1]);
        if (
          currentOrientation === "landscape" &&
          nextOrientation === "portrait"
        ) {
          // Swap the images
          [result[i], result[i + 1]] = [result[i + 1], result[i]];
          swapped = true;
        }
      }
    }
    return result;
  };

  // Calculate minimum columns needed to fit content within max height
  const calculateColumns = (images: ImageData[]): number => {
    if (images.length === 0) return 1;

    const maxHeight = 570; // Matches your largeDesktop height (in rem units, but unitless for calc)
    const margin = 20;
    const itemHeights = images.map((img) =>
      getOrientation(img) === "portrait" ? 570 : 275
    );

    let columns = 0;
    let currentHeight = 0;

    for (let height of itemHeights) {
      const addedHeight = currentHeight > 0 ? height + margin : height;
      if (currentHeight + addedHeight > maxHeight) {
        columns++;
        currentHeight = height;
      } else {
        currentHeight += addedHeight;
      }
    }

    if (currentHeight > 0) columns++;

    return Math.max(1, columns); // At least one column
  };

  // Apply the swapping logic
  const reorderedImages = swapImages(remainingImages);

  // Get calculated columns
  const numColumns = calculateColumns(reorderedImages);

  // Determine first image orientation for class
  const firstImageOrientation = getOrientation(firstImage);

  return (
    <div className={styles.container}>
      <div className={styles.gallery}>
        <div
          className={`${styles.firstImage} ${firstImageOrientation === "portrait" ? styles.portrait : ""}`}
        >
          <Image
            className={styles.image}
            src={firstImage.url}
            alt="First Image"
            fill
            style={{ objectFit: "cover" }}
            sizes="(max-width: 768px) 100vw, 50vw"
          />
        </div>

        <div
          className={styles.remainingImages}
          style={{ columnCount: numColumns }}
        >
          {reorderedImages.map((image, index) => (
            <div
              key={index}
              className={styles.imageContainer}
              data-orientation={getOrientation(image)}
            >
              <Image
                className={styles.image}
                src={image.url}
                alt={`Image ${index + 1}`}
                fill
                style={{ objectFit: "cover" }}
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            </div>
          ))}
          <div className={styles.buttonContainer}>
            <Button
              className={styles.showAllImagesButton}
              buttonChildren="Show All Images"
              buttonType="normal"
              buttonSize="small"
              name="show-all-images-btn"
              type="button"
              ariaLabel="Show All Images Button"
              autoFocus={false}
              disabled={false}
              onClick={onClick}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ImageGallery;