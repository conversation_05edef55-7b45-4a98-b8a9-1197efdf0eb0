@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    position: fixed;
    font-size: $h4;
    font-weight: bold;
    color: $danger;
    margin-top: rem(56);
    @include only(largeDesktop) {
      font-size: $h4;
      margin-top: rem(56);
      position: static;
      margin-bottom: rem(48);
    }
    @include only(smallDesktop) {
      font-size: $h4;
      margin-top: rem(56);
      position: static;
      margin-bottom: rem(48);
    }
    @include only(tablet) {
      font-size: $h4;
      margin-top: rem(56);
      position: static;
      margin-bottom: rem(48);
    }
  }

  .tabsWrapper {
    margin-top: rem(112);

    @include only(largeDesktop) {
      margin: unset;
    }
    @include only(smallDesktop) {
      margin: unset;
    }
    @include only(tablet) {
      margin: unset;
    }
  }
  .notificationsContainer {
    margin-bottom: rem(240);
    .timeRangeContainer {
      display: flex;
      align-items: center;
      margin-left: rem(32);
      margin-bottom: rem(20);
      .timeRange {
        font-weight: 500;
        margin-right: rem(4);
      }

      .count {
        display: flex;
        justify-content: center;
        align-items: center;
        width: rem(32);
        height: rem(32);
        border-radius: 50%;
        background-color: $white-two;
        color: $black-one;
        font-weight: 500;
      }
    }
    .notificationCard {
      margin-bottom: rem(32);
    }
  }
}
