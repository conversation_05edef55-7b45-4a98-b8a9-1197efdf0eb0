{"name": "wheat-tiger", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "sanity build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy"}, "keywords": ["sanity"], "dependencies": {"@sanity/client": "^6.10.0", "@sanity/icons": "^2.1.0", "@sanity/orderable-document-list": "^1.0.4", "@sanity/ui": "^1.2.0", "@sanity/vision": "^3.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-is": "^18.2.0", "sanity": "^3.0.0", "sanity-plugin-asset-source-unsplash": "^1.1.2", "styled-components": "^5.2.0"}, "devDependencies": {"@sanity/eslint-config-studio": "^2.0.1", "@types/react": "^18.0.25", "@types/styled-components": "^5.1.26", "eslint": "^8.6.0", "prettier": "^2.8.7", "typescript": "^4.0.0"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}