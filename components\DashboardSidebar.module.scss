@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: transparent;
  color: $black-four;
  border-radius: rem(40);
  width: rem(48);

  @include only(largeDesktop) {
    width: rem(342);
    height: 90vh;
    background-color: $grey-one;
    box-shadow: $shadow-one;
  }
  @include only(smallDesktop) {
    width: rem(342);
    height: 90vh;
    background-color: $grey-one;
    box-shadow: $shadow-one;
  }

  .profile {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: rem(40);
    cursor:pointer; 

    .avatar {
      margin-top: rem(48);
      margin-bottom: rem(8);
      @include only(largeDesktop) {
        margin-top: rem(32);
      }
    }

    .name {
      margin-bottom: rem(4);
    }

    .editIcon {
    }
  }
  .mainMenu {
    margin-bottom: rem(72);
  }

  .menu {
    display: flex;
    justify-content: center;
    margin-bottom: rem(24);
    @include only(largeDesktop) {
      gap: rem(16);
      justify-content: flex-start;
      align-items: center;
    }
    @include only(smallDesktop) {
      gap: rem(16);
      justify-content: flex-start;
      align-items: center;
    }

    .icon {
    }

    .menuText {
      margin: 0;
      font-weight: normal;
      font-size: rem(16);
    }
  }

  .menu:hover {
    transform: scale(1.05);
  }

  .logout {
    display: flex;
    justify-content: center;
    cursor: pointer;
    @include only(largeDesktop) {
      font-size: rem(16);
      justify-content: flex-start;
      align-items: center;
    }
    @include only(smallDesktop) {
      font-size: rem(16);
      justify-content: flex-start;
      align-items: center;
    }
  }
}
