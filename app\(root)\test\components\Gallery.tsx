"use client";
import Image from "next/image";
import styles from "./Gallery.module.scss";
import { galleryImages } from "../data/galleryImages";
import { generateGalleryLayout } from "../lib/generateGalleryLayout";

export default function Gallery() {
  const layout = generateGalleryLayout(galleryImages);

  return (
    <div className={styles.gallery}>
      {/* Grid 1 */}
      <div className={styles.grid1}>
        {layout
          .filter((l) => l.grid === 1)
          .map((l) => {
            const img = galleryImages.find((i) => i.id === l.id)!;
            return (
              <Image
                key={l.id}
                src={img.url}
                alt={img.alt || ""}
                width={img.width}
                height={img.height}
                className={styles.image}
              />
            );
          })}
      </div>

      {/* Grid 2 */}
      <div className={styles.grid2}>
        {layout
          .filter((l) => l.grid === 2)
          .map((l) => {
            const img = galleryImages.find((i) => i.id === l.id)!;
            return (
              <Image
                key={l.id}
                src={img.url}
                alt={img.alt || ""}
                width={img.width}
                height={img.height}
                className={styles.image}
              />
            );
          })}
      </div>
    </div>
  );
}
