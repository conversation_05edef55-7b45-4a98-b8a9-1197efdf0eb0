@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.button {
  background-color: $normal;
  border: none;
  color: #67787c;
  border-radius: rem(40);
  outline: none;
  box-shadow: $shadow-one;
  cursor: pointer;
}



.largeButton {
  width: rem(311);
  height: rem(56);

  @include only(largeDesktop) {
    width: rem(514);
    height: rem(56);
  }

  @include only(smallDesktop) {
    width: rem(462);
    height: rem(56);
  }

  @include only(tablet) {
    width: rem(462);
    height: rem(56);
  }

  @include only(extraLargeDesktop) {
    width: rem(514);
    height: rem(56);
  }
}
.mediumButton {
  width: rem(202);
  height: rem(56);

  @include only(largeDesktop) {
    width: rem(424);
    height: rem(56);
  }
  @include only(smallDesktop) {
    width: rem(381);
    height: rem(56);
  }
  @include only(tablet) {
    width: rem(381);
    height: rem(56);
  }
  @include only(extraLargeDesktop) {
    width: rem(424);
    height: rem(56);
  }
}
.smallButton {
  width: rem(96);
  height: rem(52);

  @include only(largeDesktop) {
    width: rem(176);
    height: rem(52);
  }

  @include only(smallDesktop) {
    width: rem(158);
    height: rem(52);
  }

  @include only(tablet) {
    width: rem(158);
    height: rem(56);
  }

  @include only(extraLargeDesktop) {
    width: rem(176);
    height: rem(56);
  }
}
.tinyButton {
  width: rem(45);
  height: rem(32);

  @include only(largeDesktop) {
    width: rem(97);
    height: rem(56);
  }
  @include only(smallDesktop) {
    width: rem(87);
    height: rem(56);
  }
}
.largeDashboardButton {
  width: rem(267);
  height: rem(56);

  @include only(largeDesktop) {
    width: rem(471);
  }
  @include only(smallDesktop) {
    width: rem(424);
  }
  @include only(tablet) {
    width: rem(424);
  }
}
.mediumDashboardButton {
  width: rem(240);
  height: rem(48);
  @include only(largeDesktop) {
    width: rem(326);
    height: rem(56);
  }
  @include only(smallDesktop) {
    width: rem(293);
    height: rem(56);
  }
  @include only(tablet) {
    width: rem(293);
    height: rem(56);
  }
}
.smallDashboardButton {
  @include only(largeDesktop) {
    width: rem(149);
    height: rem(48);
  }
  @include only(smallDesktop) {
    width: rem(134);
    height: rem(48);
  }
  @include only(tablet) {
    width: rem(134);
    height: rem(48);
  }
}
.tinyDashboardButton {
  @include only(largeDesktop) {
    width: rem(69);
    height: rem(48);
  }

  @include only(smallDesktop) {
    width: rem(62);
    height: rem(48);
  }

  @include only(tablet) {
    width: rem(62);
    height: rem(48);
  }

  @include only(extraLargeDesktop) {
    width: rem(69);
    height: rem(48);
  }
}

.round {
  border-radius: 100%;
}

.roundMedium {
  width: rem(36);
  height: rem(36);

  @include only(largeDesktop) {
    width: rem(48);
    height: rem(48);
  }
  @include only(smallDesktop) {
    width: rem(48);
    height: rem(48);
  }
  @include only(tablet) {
    width: rem(48);
    height: rem(48);
  }
}

.roundLargeDashboard {
  width: rem(32);
  height: rem(32);

  @include only(largeDesktop) {
    width: rem(48);
    height: rem(48);
  }
  @include only(smallDesktop) {
    width: rem(48);
    height: rem(48);
  }
  @include only(tablet) {
    width: rem(48);
    height: rem(48);
  }
}

.roundSmall {
  width: rem(32);
  height: rem(32);
  @include only(largeDesktop) {
    width: rem(40);
    height: rem(40);
  }
  @include only(smallDesktop) {
    width: rem(40);
    height: rem(40);
  }
  @include only(tablet) {
    width: rem(40);
    height: rem(40);
  }
}

.filter {
  width: rem(236.875);
  height: rem(48);
  border-radius: 0;
  box-shadow: none;
}

.primary {
  background-color: $primary;
}

.primary:hover {
  background-color: $primary-hover;
  color: $white-one;
}

.primary:active {
  background-color: $primary-hover;
  color: $white-one;
  outline: none;
}
.primary:disabled {
  background-color: $primary-disabled;
  color: $grey-four;
}

.normal {
  background-color: $normal;
}

.normal:hover {
  background-color: $normal-hover;
  transition: background-color 0.3s ease-in-out;
}

.normal:active {
  background-color: $normal-hover;
  outline: none;
}
.normal:disabled {
  background-color: $normal-disabled;
}

.success {
  background-color: $success;
}

.success:hover {
  background-color: $success-hover;
  transition: background-color 0.25s ease-in-out;
}

.success:focus-visible {
  // outline-offset: rem(4);
  // outline-style: solid;
  // outline-color: $success;
  // outline-width: rem(4);
}

.success:active {
  background-color: $success-hover;
  outline: none;
}
.success:disabled {
  background-color: $success-disabled;
}

.warning {
  background-color: $warning;
}

.warning:hover {
  background-color: $warning-hover;
  transition: background-color 0.25s ease-in-out;
}

.warning:focus-visible {
  // outline-offset: rem(4);
  // outline-style: solid;
  // outline-color: $warning;
  // outline-width: rem(4);
}

.warning:active {
  background-color: $warning-hover;
  outline: none;
}
.warning:disabled {
  background-color: $warning-disabled;
}

.danger {
  background-color: $danger;
  color: $white-one;
}

.danger:hover {
  background-color: $danger-hover;
  transition: background-color 0.25s ease-in-out;
}

.danger:focus-visible {
  // outline-offset: rem(4);
  // outline-style: solid;
  // outline-color: $danger;
  // outline-width: rem(4);
}

.danger:active {
  background-color: $danger-hover;
  outline: none;
}
.danger:disabled {
  background-color: $danger-disabled;
}

.info {
  background-color: $info;
}

.info:hover {
  background-color: $info-hover;
  color: $white-one;
  transition: background-color 0.25s ease-in-out;
}

.info:focus-visible {
  // outline-offset: rem(4);
  // outline-style: solid;
  // outline-color: $info;
  // outline-width: rem(4);
}

.info:active {
  background-color: $info-hover;
  outline: none;
}

.info:disabled {
  background-color: $info-disabled;
}

.linkBtn {
  background-color: transparent;
  box-shadow: none;
}

.linkBtn:hover {
  // background-color: $primary;
}

.linkBtn:focus-visible {
  // background-color: $primary;
}

.linkBtn:active {
  // background-color: $primary;
  outline: none;
}
.linkBtn:disabled {
  // background-color: $primary;
}

.icon {
  background-color: unset;
  box-shadow: none;
  width: fit-content;
  height: fit-content;
}
