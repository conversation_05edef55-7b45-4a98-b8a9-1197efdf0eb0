<!-- Generated by SpecStory -->

# Where to place loading.tsx in code (2025-07-14 13:14Z)

_**User**_

Where can I put loading.tsx in my code

---

_**Assistant**_

In a Next.js (App Router) project like yours, you can add a `loading.tsx` file to provide a loading UI for a specific route or layout. Here’s how it works:

### Where to Place `loading.tsx`

- **Route-Level Loading:**  
  Place `loading.tsx` inside any route folder (e.g., `app/(articles)/article/`) to show a loading state when navigating to that route or when its data is loading.

- **Layout-Level Loading:**  
  Place `loading.tsx` alongside a `layout.tsx` to show a loading state for all child routes under that layout.

- **Global Loading:**  
  If you want a loading UI for your entire app, place `loading.tsx` in your root `app/` directory.

### Examples

1. **For a specific page:**
   ```
   oly/app/(articles)/article/loading.tsx
   ```
   This will show when navigating to `/article`.

2. **For a section (e.g., dashboard):**
   ```
   oly/app/(dashboard)/dashboard/loading.tsx
   ```
   This will show for all routes under `/dashboard`.

3. **For the whole app:**
   ```
   oly/app/loading.tsx
   ```
   This will show for all routes if no more specific `loading.tsx` is found.

### How It Works

- Next.js will automatically use the nearest `loading.tsx` up the route tree when a route is loading.
- The file should export a React component (no need for `"use client"`; it’s always a client component).

### Example `loading.tsx`

```tsx
// oly/app/loading.tsx
export default function Loading() {
  return <div>Loading...</div>;
}
```

---

**Summary:**  
Put `loading.tsx` in any route or layout folder inside `app/` where you want a loading UI. The closer it is to the route, the more specific it is. For global loading, put it in `app/`. For section-specific, put it in the relevant subfolder.

---

_**User**_

In next js app router would you say loading should be inside every router i.e. the same loacation as every page.tsx 

---

_**Assistant**_

Great question! In Next.js App Router, you do **not** need to put a `loading.tsx` in every route folder by default. Here’s how it works and what’s recommended:

---

### How `loading.tsx` Works in Next.js App Router

- Next.js will use the **nearest** `loading.tsx` up the route tree when a route is loading.
- If you put a `loading.tsx` in a parent folder (e.g., `app/(dashboard)/dashboard/`), it will be used for all child routes unless a more specific `loading.tsx` exists in a subfolder.
- You only need to add a `loading.tsx` in a route folder if you want a **custom loading UI** for that specific route.

---

### Best Practices

- **Default/Global Loading:**  
  Place a `loading.tsx` in the root `app/` directory for a global loading UI.
- **Section-Specific Loading:**  
  Place a `loading.tsx` in a section folder (e.g., `app/(dashboard)/dashboard/`) if you want a different loading UI for that section.
- **Page-Specific Loading:**  
  Only add a `loading.tsx` next to a `page.tsx` if you want a unique loading experience for that page.

---

### Example Structure

```
app/
  loading.tsx                // Global loading
  (dashboard)/
    dashboard/
      loading.tsx            // Dashboard section loading
      page.tsx
      settings/
        page.tsx             // Inherits dashboard loading
      profile/
        loading.tsx          // Custom loading for profile
        page.tsx
```

- `app/loading.tsx` is used everywhere unless overridden.
- `app/(dashboard)/dashboard/loading.tsx` is used for all dashboard routes unless overridden.
- `app/(dashboard)/dashboard/profile/loading.tsx` is only used for the profile page.

---

### Summary

- You **do not** need to put `loading.tsx` in every route.
- Only add it where you want a custom loading UI.
- The nearest `loading.tsx` up the folder tree will be used automatically.

If you want to see how this is handled in your codebase or want to add a `loading.tsx` somewhere, let me know!

---

