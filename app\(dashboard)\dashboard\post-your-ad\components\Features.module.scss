@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow-y: auto;
  overflow-x: hidden;
  width: rem(311);

  @include only(largeDesktop) {
    width: rem(1098);
  }
  @include only(smallDesktop) {
    width: rem(960);
  }
  @include only(tablet) {
    width: rem(960);
  }

  .form {
    display: flex;
    flex-direction: column;
    align-items: center;
  

    .formElements {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      .errorMessage {
        color: $danger;
        margin-bottom: rem(8);
      }
      .conditionsContainer {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: rem(20);
      }

      .selecteDetailContainer {
        margin-bottom: rem(20);
      }

      .chosenDetailContainer {
        padding: rem(16) rem(32);
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: $white-one;
        border-radius: rem(40) rem(40) rem(44) rem(44);
        width: rem(548);
        min-height: rem(232);
        margin-bottom: rem(48);

        .chosenDetailTitle {
          color: $black-four;
          margin-top: rem(24);
          margin-bottom: rem(8);
          text-align: center;
        }

        .chosenDetailDescription {
          color: $grey-four;
          text-align: center;
          margin-bottom: rem(48);
        }

        .chosenDetail {
          margin-bottom: rem(20);

          .chosenDetailInput {
            background-color: $white-two;
            width: rem(471);
          }
        }
      }

      .customDetailContainer {
        margin-bottom: rem(20);
      }
    }
  }

  .details {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: rem(648);
    margin-bottom: rem(48);
    .detail {
      display: flex;
      align-items: center;
      margin-bottom: rem(16);
      width: rem(480);

      .detailButtons {
        display: flex;
        margin-right: rem(16);
        .detailButton {
          display: flex;
          justify-content: center;
          align-items: center;
          width: rem(40);
          height: rem(40);
          border: none;
          background-color: $white-one;
        }

        .detailButton:hover {
          box-shadow: $shadow-one;
          transform: scale(1.1);
        }
        .editButtonContainer {
        }

        .deleteButtonContainer {
          margin-left: rem(16);
        }
      }

      .detailText {
        font-weight: normal;
      }
    }

    .editMode {
      position: relative;
      width: rem(514);
      margin-bottom: rem(16);

      .editDetail {
        width: rem(514);
        height: rem(40);
        text-align: start;
        color: $black-one;
      }

      .submitButton {
        position: absolute;
        top: 50%;
        display: flex;
        justify-content: center;
        align-items: center;
        width: rem(40);
        height: rem(40);
        border-radius: 50%;
        transform: translateY(-50%);
        right: rem(8);
        z-index: 10;
        background-color: $white-one;
        cursor: pointer;
        box-shadow:
          10px 10px 20px 0px rgba(169, 196, 203, 0.5),
          5px 5px 10px 0px rgba(169, 196, 203, 0.25);

        .iconContainer {
          margin-top: rem(4);
        }
      }
    }
  }

  .submitButtonContainer {
  }

  .buttons {
    display: flex;
    flex-direction: column;
    margin-bottom: rem(124);

    .proceedButton {
      margin-bottom: rem(20);
    }
  }
}
