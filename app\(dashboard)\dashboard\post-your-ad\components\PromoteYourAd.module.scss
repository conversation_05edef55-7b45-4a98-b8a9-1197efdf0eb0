@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;

  @include only(largeDesktop) {
    width: rem(1098);
  }
  @include only(smallDesktop) {
    width: rem(960);
  }
  @include only(tablet) {
    width: rem(960);
  }

  .title {
    margin-top: rem(52);
    margin-bottom: rem(48);
    color: $danger;
    font-weight: 500;
    font-size: $h4;

    @include only(largeDesktop) {
      font-size: $h4;
      margin-top: rem(124);
      margin-bottom: rem(48);
    }
    @include only(smallDesktop) {
      font-size: $h4;
      margin-top: rem(124);
      margin-bottom: rem(48);
    }
    @include only(tablet) {
      font-size: $h4;
      margin-top: rem(124);
      margin-bottom: rem(48);
    }
  }

  .cards {
    .cardContainer {
      margin-bottom: rem(48);
    }
  }

  .promotionTotal {
    display: flex;
    align-items: center;
    gap: rem(20);
    margin-bottom: rem(58);
    .promotion {
    }

    .price {
      font-weight: 500;
      font-size: $h3;
    }
  }

  .buttons {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: rem(48);
    margin-bottom: rem(124);

    .proceedButton {
      margin-bottom: rem(20);
    }

    .backButton {
    }
  }
}
