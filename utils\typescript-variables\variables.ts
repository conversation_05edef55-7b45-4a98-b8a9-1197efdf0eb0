
export const variables = {
  whiteOne: "#ffffff",
  whiteTwo: "#f9fcfd",
  whiteThree: "#f3f7fa",
  whiteFour: "#edf2f7",
  whiteFive: "#e9fcff",
  blackOne: "#000000",
  blackTwo: "#12161f",
  blackThree: "#1c2230",
  blackFour: "#434b4d",
  blueOne: "#c9e5e8",
  blueOneComplimentary: "#e8ccc9",
  blueOneComplimentaryTwo: "#f8dad7",
  greyOne: "#e4e6e7",
  greyTwo: "#afb0b0",
  greyThree: "#dfddd6",
  greyFour: "#7e9094",
  primary: "#ccf6ff",
  primaryHover: "#14d6ff",
  primaryDisabled: "#ebfcff",
  normal: "#f3f7fa",
  normalHover: "#ffffff",
  normalDisabled: "#f3f7fa",
  success: "#ccffed",
  successHover: "#14ffae",
  successDisabled: "#e5fff6",
  warning: "#ffeacc",
  warningHover: "#ff9d14",
  warningDisabled: "#fff4e5",
  danger: "#ff6b4c",
  dangerHover: "#ff3c14",
  dangerDisabled: "#ffaa99",
  info: "#cce4ff",
  infoHover: "#1482ff",
  infoDisabled: "#e5f1ff",
  link: "#10badc",
  body: 16,
  h0: 72,
  h1: 48,
  h2: 32,
  h3: 24,
  h4: 18,
  minMobile: 320,
  maxMobile: 767,
  minTablet: 768,
  maxTablet: 1199,
  minSmallDesktop: 1200,
  maxSmallDesktop: 1439,
  minLargeDesktop: 1440,
  maxLargeDesktop: 1951,
  minExtraLargeDesktop: 1952,
  maxExtraLargeDesktop: null,
  shadowGradient: "linear-gradient(145deg, #f0f0f0, #cacaca)",
  shadowOne: "0px 1px 3px 0px rgba(180, 191, 203, 0.2), 0px 5px 5px 0px rgba(180, 191, 203, 0.17), 0px 11px 7px 0px rgba(180, 191, 203, 0.1), 0px 20px 8px 0px rgba(180, 191, 203, 0.03), 0px 30px 9px 0px rgba(180, 191, 203, 0)",
  shadowTwo: "-5px -5px 10px 0px #fff inset, 5px 5px 10px 0px rgba(170, 186, 204, 0.5) inset, 2px 2px 4px 0px rgba(120, 145, 171, 0.25) inset, -2px -2px 4px 0px rgba(255, 255, 255, 0.5) inset",
  shadowThree: "-5px -5px 10px 0px #fff, 5px 5px 10px 0px rgba(170, 186, 204, 0.5), 2px 2px 4px 0px rgba(120, 145, 171, 0.25), -2px -2px 4px 0px rgba(255, 255, 255, 0.5)",
  shadowFour: "0px 1px 3px 0px rgba(207, 211, 211, 0.98), 0px 5px 5px 0px rgba(207, 211, 211, 0.85), 0px 11px 7px 0px rgba(207, 211, 211, 0.5), 0px 20px 8px 0px rgba(207, 211, 211, 0.15), 0px 30px 9px 0px rgba(207, 211, 211, 0.02)",
  shadowFive: "0px 11px 25px 0px rgba(207, 211, 211, 0.2), 0px 45px 45px 0px rgba(207, 211, 211, 0.17), 0px 101px 61px 0px rgba(207, 211, 211, 0.1), 0px 180px 72px 0px rgba(207, 211, 211, 0.03), 0px 282px 79px 0px rgba(207, 211, 211, 0)",
  shadowSix: "10px 10px 20px 0px rgba(169, 196, 203, 0.5), 5px 5px 10px 0px rgba(169, 196, 203, 0.25)",
  shadowSeven: "-10px -10px 20px #fff, 10px 10px 20px rgba(169, 196, 203, 0.5), 5px 5px 10px rgba(169, 196, 203, 0.25), 0px 420px 168px 0px rgba(0, 0, 0, 0.01), -5px -5px 10px rgba(255, 255, 255, 0.5)",
  shadowEight: "0px 1px 3px 0px rgba(180, 191, 203, 0.98), 0px 5px 5px 0px rgba(180, 191, 203, 0.85), 0px 11px 7px 0px rgba(180, 191, 203, 0.5), 0px 20px 8px 0px rgba(180, 191, 203, 0.15), 0px 30px 9px 0px rgba(180, 191, 203, 0.02)",
  transitionOne: "all 0.2s ease",
  transitionTwo: "all 0.3s ease",
  transitionThree: "all 0.5s ease"
} as const;

export type Variables = {
  whiteOne: string;
  whiteTwo: string;
  whiteThree: string;
  whiteFour: string;
  whiteFive: string;
  blackOne: string;
  blackTwo: string;
  blackThree: string;
  blackFour: string;
  blueOne: string;
  blueOneComplimentary: string;
  blueOneComplimentaryTwo: string;
  greyOne: string;
  greyTwo: string;
  greyThree: string;
  greyFour: string;
  primary: string;
  primaryHover: string;
  primaryDisabled: string;
  normal: string;
  normalHover: string;
  normalDisabled: string;
  success: string;
  successHover: string;
  successDisabled: string;
  warning: string;
  warningHover: string;
  warningDisabled: string;
  danger: string;
  dangerHover: string;
  dangerDisabled: string;
  info: string;
  infoHover: string;
  infoDisabled: string;
  link: string;
  body: number;
  h0: number;
  h1: number;
  h2: number;
  h3: number;
  h4: number;
  minMobile: number;
  maxMobile: number;
  minTablet: number;
  maxTable: number;
  minSmallDesktop: number;
  maxSmallDesktop: number;
  minLargeDesktop: number;
  maxLargeDesktop: number;
  minExtraLargeDesktop: number;
  maxExtraLargeDesktop: null;
  shadowGradient: string;
  shadowOne: string;
  shadowTwo: string;
  shadowThree: string;
  shadowFour: string;
  shadowFive: string;
  shadowSix: string;
  shadowSeven: string;
  shadowEight: string;
  transitionOne: string;
  transitionTwo: string;
  transitionThree: string;
};
