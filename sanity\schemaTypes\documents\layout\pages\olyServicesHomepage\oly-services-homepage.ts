import { defineType, defineField } from 'sanity';

export const olyServicesHomepage = defineType({
  name: 'olyServicesHomepage',
  title: 'Oly Services Homepage',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Oly Services Homepage Configuration Title',
      type: 'string',
      initialValue: 'Main Oly Services Homepage Configuration',
      description: 'Internal title for this Oly Services homepage configuration.',
    }),

    defineField({
      name: 'isActive',
      title: 'Is Active',
      type: 'boolean',
      initialValue: true,
      description: 'Whether this Oly Services homepage configuration is currently active.',
    }),

    defineField({
      name: 'heroSection',
      title: 'Hero Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Hero Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the hero section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Services Hero Section',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Hero Section Reference',
          type: 'reference',
          to: [{ type: 'heroSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the hero section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 1,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Hero section configuration and reference.',
    }),

    defineField({
      name: 'moreFromOlySection',
      title: 'More from Oly Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable More from Oly Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the More from Oly section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'More from Oly Services',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'More from Oly Section Reference',
          type: 'reference',
          to: [{ type: 'moreFromOlySection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the More from Oly section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 2,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'More from Oly section configuration and reference.',
    }),

    defineField({
      name: 'featuredServicesSection',
      title: 'Featured Services Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Services Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Services section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Services',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Services Section Reference',
          type: 'reference',
          to: [{ type: 'featuredServicesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Services section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 3,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Featured Services section configuration and reference.',
    }),

    defineField({
      name: 'featuredCategoriesSection',
      title: 'Featured Categories Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Categories Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Categories section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Service Categories',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Categories Section Reference',
          type: 'reference',
          to: [{ type: 'featuredCategoriesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Categories section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 4,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Featured Categories section configuration and reference.',
    }),

    defineField({
      name: 'featuredProvidersSection',
      title: 'Featured Providers Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Providers Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Providers section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Service Providers',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Providers Section Reference',
          type: 'reference',
          to: [{ type: 'featuredProvidersSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Providers section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 5,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Featured Providers section configuration and reference.',
    }),

    defineField({
      name: 'serviceRequestsSection',
      title: 'Service Requests Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Service Requests Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Service Requests section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Recent Service Requests',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Service Requests Section Reference',
          type: 'reference',
          to: [{ type: 'serviceRequestsSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Service Requests section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 6,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Service Requests section configuration and reference.',
    }),

    defineField({
      name: 'serviceToolsSection',
      title: 'Service Tools Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Service Tools Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Service Tools section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Service Tools & Calculators',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Service Tools Section Reference',
          type: 'reference',
          to: [{ type: 'serviceToolsSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Service Tools section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 7,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Service Tools section configuration and reference.',
    }),

    defineField({
      name: 'adSection',
      title: 'Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Ad section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Services Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Ad Section Reference',
          type: 'reference',
          to: [{ type: 'adSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 8,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Ad section configuration and reference.',
    }),

    defineField({
      name: 'topAdSection',
      title: 'Top Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Top Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Top Ad section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Top Services Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Top Ad Section Reference',
          type: 'reference',
          to: [{ type: 'topAdSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Top Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 0,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Top Ad section configuration and reference.',
    }),

    defineField({
      name: 'bottomAdSection',
      title: 'Bottom Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Bottom Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Bottom Ad section on the Oly Services homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Bottom Services Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Bottom Ad Section Reference',
          type: 'reference',
          to: [{ type: 'bottomAdSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Bottom Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 9,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Services homepage.',
        }),
      ],
      description: 'Bottom Ad section configuration and reference.',
    }),

    defineField({
      name: 'servicesSettings',
      title: 'Services-Specific Settings',
      type: 'object',
      fields: [
        defineField({
          name: 'defaultServiceType',
          title: 'Default Service Type',
          type: 'string',
          options: {
            list: [
              { title: 'All Services', value: 'all' },
              { title: 'Home Services', value: 'home' },
              { title: 'Professional Services', value: 'professional' },
              { title: 'Personal Services', value: 'personal' },
              { title: 'Business Services', value: 'business' },
              { title: 'Digital Services', value: 'digital' },
              { title: 'Health & Wellness', value: 'health' },
            ],
          },
          initialValue: 'all',
          description: 'Default service type filter for the homepage.',
        }),
        defineField({
          name: 'showPriceRanges',
          title: 'Show Price Ranges',
          type: 'boolean',
          initialValue: true,
          description: 'Display price range filters on the services homepage.',
        }),
        defineField({
          name: 'showLocationFilters',
          title: 'Show Location Filters',
          type: 'boolean',
          initialValue: true,
          description: 'Display location-based service filters on the services homepage.',
        }),
        defineField({
          name: 'showAvailabilityFilters',
          title: 'Show Availability Filters',
          type: 'boolean',
          initialValue: true,
          description: 'Display availability filters (immediate, scheduled, emergency).',
        }),
        defineField({
          name: 'enableServiceQuotes',
          title: 'Enable Service Quotes',
          type: 'boolean',
          initialValue: true,
          description: 'Enable service quote request functionality.',
        }),
        defineField({
          name: 'enableProviderRatings',
          title: 'Enable Provider Ratings',
          type: 'boolean',
          initialValue: true,
          description: 'Display service provider ratings and reviews.',
        }),
        defineField({
          name: 'showServiceComparison',
          title: 'Show Service Comparison',
          type: 'boolean',
          initialValue: true,
          description: 'Enable service and provider comparison tools.',
        }),
        defineField({
          name: 'enableInstantBooking',
          title: 'Enable Instant Booking',
          type: 'boolean',
          initialValue: true,
          description: 'Enable instant booking for available services.',
        }),
        defineField({
          name: 'showServiceGuarantees',
          title: 'Show Service Guarantees',
          type: 'boolean',
          initialValue: true,
          description: 'Display service guarantees and warranties information.',
        }),
      ],
      description: 'Settings specific to the services homepage.',
    }),

    defineField({
      name: 'seoSettings',
      title: 'SEO Settings',
      type: 'object',
      fields: [
        defineField({
          name: 'metaTitle',
          title: 'Meta Title',
          type: 'string',
          validation: (Rule) => Rule.max(60),
          description: 'SEO title for the Oly Services homepage (max 60 characters).',
        }),
        defineField({
          name: 'metaDescription',
          title: 'Meta Description',
          type: 'text',
          rows: 3,
          validation: (Rule) => Rule.max(160),
          description: 'SEO description for the Oly Services homepage (max 160 characters).',
        }),
        defineField({
          name: 'keywords',
          title: 'Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            layout: 'tags',
          },
          description: 'SEO keywords for the Oly Services homepage.',
        }),
      ],
      description: 'SEO settings for the Oly Services homepage.',
    }),

    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      initialValue: new Date().toISOString(),
      description: 'When this Oly Services homepage configuration was published.',
    }),
  ],

  preview: {
    select: {
      title: 'title',
      isActive: 'isActive',
      heroEnabled: 'heroSection.isEnabled',
      moreFromOlyEnabled: 'moreFromOlySection.isEnabled',
      featuredServicesEnabled: 'featuredServicesSection.isEnabled',
      featuredCategoriesEnabled: 'featuredCategoriesSection.isEnabled',
      featuredProvidersEnabled: 'featuredProvidersSection.isEnabled',
      serviceRequestsEnabled: 'serviceRequestsSection.isEnabled',
      serviceToolsEnabled: 'serviceToolsSection.isEnabled',
      defaultServiceType: 'servicesSettings.defaultServiceType',
    },
    prepare(selection) {
      const { 
        title, 
        isActive, 
        heroEnabled, 
        moreFromOlyEnabled, 
        featuredServicesEnabled, 
        featuredCategoriesEnabled, 
        featuredProvidersEnabled,
        serviceRequestsEnabled,
        serviceToolsEnabled,
        defaultServiceType 
      } = selection;
      
      const enabledSections = [
        heroEnabled,
        moreFromOlyEnabled,
        featuredServicesEnabled,
        featuredCategoriesEnabled,
        featuredProvidersEnabled,
        serviceRequestsEnabled,
        serviceToolsEnabled,
      ].filter(Boolean).length;
      
      return {
        title: title || 'Oly Services Homepage Configuration',
        subtitle: `${isActive ? '✅ Active' : '❌ Inactive'} - ${enabledSections} sections enabled (${defaultServiceType || 'all'} services)${serviceToolsEnabled ? ' + Tools' : ''}`,
        media: '🔧',
      };
    },
  },
});