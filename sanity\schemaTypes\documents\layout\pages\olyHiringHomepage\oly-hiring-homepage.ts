import { defineType, defineField } from 'sanity';

export const olyHiringHomepage = defineType({
  name: 'olyHiringHomepage',
  title: 'Oly Hiring Homepage',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Oly Hiring Homepage Configuration Title',
      type: 'string',
      initialValue: 'Main Oly Hiring Homepage Configuration',
      description: 'Internal title for this Oly Hiring homepage configuration.',
    }),

    defineField({
      name: 'isActive',
      title: 'Is Active',
      type: 'boolean',
      initialValue: true,
      description: 'Whether this Oly Hiring homepage configuration is currently active.',
    }),

    defineField({
      name: 'heroSection',
      title: 'Hero Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Hero Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the hero section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Hiring Hero Section',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Hero Section Reference',
          type: 'reference',
          to: [{ type: 'heroSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the hero section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 1,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Hero section configuration and reference.',
    }),

    defineField({
      name: 'moreFromOlySection',
      title: 'More from Oly Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable More from Oly Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the More from Oly section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'More from Oly Hiring',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'More from Oly Section Reference',
          type: 'reference',
          to: [{ type: 'moreFromOlySection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the More from Oly section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 2,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'More from Oly section configuration and reference.',
    }),

    defineField({
      name: 'featuredServicesSection',
      title: 'Featured Services Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Services Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Services section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Hiring Services',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Services Section Reference',
          type: 'reference',
          to: [{ type: 'featuredServicesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Services section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 3,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Featured Services section configuration and reference.',
    }),

    defineField({
      name: 'featuredCategoriesSection',
      title: 'Featured Categories Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Categories Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Categories section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Job Categories',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Categories Section Reference',
          type: 'reference',
          to: [{ type: 'featuredCategoriesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Categories section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 4,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Featured Categories section configuration and reference.',
    }),

    defineField({
      name: 'featuredJobsSection',
      title: 'Featured Jobs Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Jobs Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Jobs section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Job Opportunities',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Jobs Section Reference',
          type: 'reference',
          to: [{ type: 'featuredJobsSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Jobs section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 5,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Featured Jobs section configuration and reference.',
    }),

    defineField({
      name: 'careerResourcesSection',
      title: 'Career Resources Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Career Resources Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Career Resources section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Career Resources & Tools',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Career Resources Section Reference',
          type: 'reference',
          to: [{ type: 'careerResourcesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Career Resources section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 6,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Career Resources section configuration and reference.',
    }),

    defineField({
      name: 'employersSection',
      title: 'Employers Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Employers Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Employers section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Employers',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Employers Section Reference',
          type: 'reference',
          to: [{ type: 'employersSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Employers section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 7,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Employers section configuration and reference.',
    }),

    defineField({
      name: 'adSection',
      title: 'Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Ad section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Hiring Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Ad Section Reference',
          type: 'reference',
          to: [{ type: 'adSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 8,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Ad section configuration and reference.',
    }),

    defineField({
      name: 'topAdSection',
      title: 'Top Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Top Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Top Ad section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Top Hiring Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Top Ad Section Reference',
          type: 'reference',
          to: [{ type: 'topAdSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Top Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 0,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Top Ad section configuration and reference.',
    }),

    defineField({
      name: 'bottomAdSection',
      title: 'Bottom Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Bottom Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Bottom Ad section on the Oly Hiring homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Bottom Hiring Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Bottom Ad Section Reference',
          type: 'reference',
          to: [{ type: 'bottomAdSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Bottom Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 9,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the Oly Hiring homepage.',
        }),
      ],
      description: 'Bottom Ad section configuration and reference.',
    }),

    defineField({
      name: 'hiringSettings',
      title: 'Hiring-Specific Settings',
      type: 'object',
      fields: [
        defineField({
          name: 'defaultJobType',
          title: 'Default Job Type',
          type: 'string',
          options: {
            list: [
              { title: 'All Jobs', value: 'all' },
              { title: 'Full-time', value: 'full-time' },
              { title: 'Part-time', value: 'part-time' },
              { title: 'Contract', value: 'contract' },
              { title: 'Freelance', value: 'freelance' },
              { title: 'Internship', value: 'internship' },
              { title: 'Remote', value: 'remote' },
            ],
          },
          initialValue: 'all',
          description: 'Default job type filter for the homepage.',
        }),
        defineField({
          name: 'showSalaryRanges',
          title: 'Show Salary Ranges',
          type: 'boolean',
          initialValue: true,
          description: 'Display salary range filters on the hiring homepage.',
        }),
        defineField({
          name: 'showLocationFilters',
          title: 'Show Location Filters',
          type: 'boolean',
          initialValue: true,
          description: 'Display location-based job filters on the hiring homepage.',
        }),
        defineField({
          name: 'showExperienceLevels',
          title: 'Show Experience Levels',
          type: 'boolean',
          initialValue: true,
          description: 'Display experience level filters (entry, mid, senior).',
        }),
        defineField({
          name: 'enableResumeBuilder',
          title: 'Enable Resume Builder',
          type: 'boolean',
          initialValue: true,
          description: 'Enable resume building tools integration.',
        }),
        defineField({
          name: 'enableSalaryCalculator',
          title: 'Enable Salary Calculator',
          type: 'boolean',
          initialValue: true,
          description: 'Enable salary estimation and comparison tools.',
        }),
        defineField({
          name: 'showCompanyProfiles',
          title: 'Show Company Profiles',
          type: 'boolean',
          initialValue: true,
          description: 'Display detailed company profile information.',
        }),
        defineField({
          name: 'enableJobAlerts',
          title: 'Enable Job Alerts',
          type: 'boolean',
          initialValue: true,
          description: 'Enable job alert subscription functionality.',
        }),
      ],
      description: 'Settings specific to the hiring homepage.',
    }),

    defineField({
      name: 'seoSettings',
      title: 'SEO Settings',
      type: 'object',
      fields: [
        defineField({
          name: 'metaTitle',
          title: 'Meta Title',
          type: 'string',
          validation: (Rule) => Rule.max(60),
          description: 'SEO title for the Oly Hiring homepage (max 60 characters).',
        }),
        defineField({
          name: 'metaDescription',
          title: 'Meta Description',
          type: 'text',
          rows: 3,
          validation: (Rule) => Rule.max(160),
          description: 'SEO description for the Oly Hiring homepage (max 160 characters).',
        }),
        defineField({
          name: 'keywords',
          title: 'Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            layout: 'tags',
          },
          description: 'SEO keywords for the Oly Hiring homepage.',
        }),
      ],
      description: 'SEO settings for the Oly Hiring homepage.',
    }),

    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      initialValue: new Date().toISOString(),
      description: 'When this Oly Hiring homepage configuration was published.',
    }),
  ],

  preview: {
    select: {
      title: 'title',
      isActive: 'isActive',
      heroEnabled: 'heroSection.isEnabled',
      moreFromOlyEnabled: 'moreFromOlySection.isEnabled',
      featuredServicesEnabled: 'featuredServicesSection.isEnabled',
      featuredCategoriesEnabled: 'featuredCategoriesSection.isEnabled',
      featuredJobsEnabled: 'featuredJobsSection.isEnabled',
      careerResourcesEnabled: 'careerResourcesSection.isEnabled',
      employersEnabled: 'employersSection.isEnabled',
      defaultJobType: 'hiringSettings.defaultJobType',
    },
    prepare(selection) {
      const { 
        title, 
        isActive, 
        heroEnabled, 
        moreFromOlyEnabled, 
        featuredServicesEnabled, 
        featuredCategoriesEnabled, 
        featuredJobsEnabled,
        careerResourcesEnabled,
        employersEnabled,
        defaultJobType 
      } = selection;
      
      const enabledSections = [
        heroEnabled,
        moreFromOlyEnabled,
        featuredServicesEnabled,
        featuredCategoriesEnabled,
        featuredJobsEnabled,
        careerResourcesEnabled,
        employersEnabled,
      ].filter(Boolean).length;
      
      return {
        title: title || 'Oly Hiring Homepage Configuration',
        subtitle: `${isActive ? '✅ Active' : '❌ Inactive'} - ${enabledSections} sections enabled (${defaultJobType || 'all'} jobs)${careerResourcesEnabled ? ' + Resources' : ''}`,
        media: '💼',
      };
    },
  },
});