@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.swiper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;

  @include only(largeDesktop) {
    width: 100%;
    height: 100%;
  }
  @include only(smallDesktop) {
    width: 100%;
    height: 100%;
  }
  
  .slideContainer {
    display: flex;
    width: 100%;
    .slide {
      display: flex;
      justify-content: center;
      height: 100%;
      width: 100%;

      @include only(largeDesktop) {
        width: 100%;
        height: 100%;
      }
      @include only(smallDesktop) {
        width: 100%;
        height: 100%;
      }
    }
  }
  .navButtons {
    position: absolute;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: rem(16);
    margin-right: rem(320);
    margin-bottom: rem(48);
    z-index: 10;

    @include only(largeDesktop) {
      margin-right: rem(320);
      margin-bottom: rem(48);
    }
    @include only(smallDesktop) {
      margin-right: rem(210);
      margin-bottom: rem(48);
    }
  }

  
  :global(.swiper-pagination-bullet) {
      background-color: #cccccc; // Color for inactive bullets
      opacity: 0.6; // Optional: adjust opacity for inactive bullets
    }
    :global(.swiper-pagination-bullet-active) {
      background-color: $primary-hover; // Color for active bullet
      opacity: 1; // Full opacity for active bullet
    }
}