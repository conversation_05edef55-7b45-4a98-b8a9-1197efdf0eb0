@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.tabs {
  display: flex;
  border-radius: rem(40);
  width: 100%;
  column-gap: rem(8);
  cursor: pointer;

  .tab {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    width: rem(100);
    height: rem(56);
    margin-bottom: rem(20);
    border-radius: rem(40);
    background-color: $white-three;
    box-shadow: $shadow-one;

    .count {
      position: absolute;
      top: 50%;
      right: 10%;
      display: flex;
      justify-content: center;
      align-items: center;
      transform: translate(50%, -50%);
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      // color: $black-three;
      font-weight: 500;
      width: rem(32);
      height: rem(32);
      border-radius: 50%;
      // box-shadow: $shadow-one;
    }
  }

  .tab:hover {
    background-color: $white-two;
  }
  .tab:active {
    background-color: $primary;
  }

  .active {
    background-color: $primary;
  }
  .active:hover {
    background-color: $primary;
  }

  .options {
    position: relative;
    background-color: $white-four;
    display: flex;
    flex-direction: column;
    align-items: center;
    max-height: rem(400);
    cursor: pointer;
    overflow-y: auto;
    // padding-top: rem(8);
    // padding-bottom: rem(8);
    margin-top: rem(-7);
    z-index: -1;

    @include only(largeDesktop) {
      margin-top: rem(24);
      margin-bottom: rem(16);
    }

    .option {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 96%;
      height: rem(40);
      border-radius: rem(40);
      margin-bottom: rem(8);
      background-color: $white-two;
      box-shadow: $shadow-one;
    }

    .option:hover {
      background-color: $white-three;
    }
  }

  .options::-webkit-scrollbar-track {
    border-radius: rem(40);
    margin-block: rem(30);
  }

  .hideOptions {
    display: none;
  }
}
