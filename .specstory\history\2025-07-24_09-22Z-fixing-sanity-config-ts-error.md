<!-- Generated by SpecStory -->

# Fixing sanity.config.ts error (2025-07-24 09:22Z)

_**User**_

I am getting this error in sanity.config.tssanity/structure"' has no exported member named 'structureTool'. Did you mean 'structure'?

---

_**Assistant**_

The error message:

> '"sanity/structure"' has no exported member named 'structureTool'. Did you mean 'structure'?  

means that the package sanity/structure does not export a member called structureTool. Instead, it exports structure.

### How to fix

- If you want to use the structure tool, you should import it from sanity/structure, but the correct export is just structure, not structureTool.
- The structureTool is not a valid export in the latest Sanity v3+ API.

#### Your current code:
```ts
import {structureTool} from 'sanity/structure'
...
plugins: [
  structureTool({structure}),
  ...
]
```

#### Correct usage:
You should use the structure plugin like this:
```ts
import {structure} from 'sanity/structure'
...
plugins: [
  structure({structure}),
  ...
]
```
But, the structure export is a plugin itself, so you just pass it to plugins, and you don't need to call it as a function unless you want to customize it.

#### Example fix:
```ts
import {structure} from 'sanity/structure'
...
plugins: [
  structure({structure}),
  visionTool({defaultApiVersion: apiVersion}),
]
```
Or, if you want the default structure:
```ts
import {structure} from 'sanity/structure'
...
plugins: [
  structure(),
  visionTool({defaultApiVersion: apiVersion}),
]
```

#### If you want to use a custom structure:
You can pass your custom structure as an option:
```ts
import {structure} from 'sanity/structure'
import {structure as myStructure} from './sanity/structure'
...
plugins: [
  structure({structure: myStructure}),
  visionTool({defaultApiVersion: apiVersion}),
]
```

### Summary

- Replace structureTool with structure in your import and plugin usage.
- Make sure you are using the correct version of the Sanity Studio and its plugins.

Would you like me to apply this fix to your sanity.config.ts?

---

_**User**_

You said: You should use the structure plugin like this: but you didn't show me how

---

_**Assistant**_

Thank you for pointing that out! Here’s exactly how you should use the structure plugin in your sanity.config.ts:

### 1. Import the correct export:
```ts
import {structure} from 'sanity/structure'
```

### 2. Use it in the plugins array:
If you want to use your custom structure (as you are currently doing):
```ts
import {structure as myStructure} from './sanity/structure'

export default defineConfig({
  // ...other config...
  plugins: [
    structure({structure: myStructure}),
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

If you want to use the default structure:
```ts
export default defineConfig({
  // ...other config...
  plugins: [
    structure(),
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

**Key point:**  
- Replace `structureTool({structure})` with `structure({structure: myStructure})` (if using your custom structure).
- Make sure your import from `'sanity/structure'` is `{structure}` not `{structureTool}`.

Would you like me to update your file with this fix?

---

_**User**_

do I have to use myStructure?

---

_**Assistant**_

No, you do not have to use `myStructure` (your custom structure).  
You have two options:

---

### 1. Use the Default Structure

If you are happy with Sanity Studio’s default desk structure, you can simply do:

```ts
import {structure} from 'sanity/structure'

export default defineConfig({
  // ...other config...
  plugins: [
    structure(),
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

---

### 2. Use a Custom Structure

If you want to customize the Studio’s desk structure (for example, to change how documents are grouped or displayed), you can define your own structure in a file (like `./sanity/structure.ts`) and use it like this:

```ts
import {structure} from 'sanity/structure'
import {structure as myStructure} from './sanity/structure'

export default defineConfig({
  // ...other config...
  plugins: [
    structure({structure: myStructure}),
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

---

**Summary:**  
- Use `structure()` for the default.
- Use `structure({structure: myStructure})` for a custom structure.

If you don’t need any customization, you do NOT need to use `myStructure`.  
Let me know if you want to use the default or your custom structure, and I can update your file accordingly!

---

_**User**_

Show me the two options with examples

---

_**Assistant**_

Absolutely! Here are both options with clear examples for your sanity.config.ts:

---

## 1. Using the Default Structure

If you want to use Sanity Studio’s default desk structure, your config should look like this:

```ts
'use client'

import {visionTool} from '@sanity/vision'
import {defineConfig} from 'sanity'
import {structure} from 'sanity/structure'

import {apiVersion, dataset, projectId} from './sanity/env'
import {schema} from './sanity/schemaTypes'

export default defineConfig({
  basePath: '/studio',
  projectId,
  dataset,
  schema,
  plugins: [
    structure(), // <--- Default structure
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

---

## 2. Using a Custom Structure

If you want to customize the desk structure, you need a custom structure file (e.g., ./sanity/structure.ts).  
Assuming you have something like this in ./sanity/structure.ts:

```ts
// ./sanity/structure.ts
import {StructureBuilder} from 'sanity/structure'

export const structure = (S: StructureBuilder) =>
  S.list()
    .title('Custom Content')
    .items([
      // ...your custom structure here
    ])
```

Then, your sanity.config.ts would look like this:

```ts
'use client'

import {visionTool} from '@sanity/vision'
import {defineConfig} from 'sanity'
import {structure} from 'sanity/structure'
import {structure as myStructure} from './sanity/structure'

import {apiVersion, dataset, projectId} from './sanity/env'
import {schema} from './sanity/schemaTypes'

export default defineConfig({
  basePath: '/studio',
  projectId,
  dataset,
  schema,
  plugins: [
    structure({structure: myStructure}), // <--- Custom structure
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

---

**Summary:**  
- Use `structure()` for the default desk structure.
- Use `structure({structure: myStructure})` for a custom desk structure.

Let me know if you want to see an example of a custom structure file as well!

---

_**User**_

now I am getting this error from elsewhere in the code: TypeError: Cannot read properties of undefined (reading 'list')
    at structure (http://localhost:3000/_next/static/chunks/_215f1a95._.js:50:26)
    at [project]/sanity.config.ts [app-client] (ecmascript) (http://localhost:3000/_next/static/chunks/_215f1a95._.js:85:145)
    at http://localhost:3000/_next/static/chunks/_fdad9c64._.js:732:27
    at runModuleExecutionHooks (http://localhost:3000/_next/static/chunks/_fdad9c64._.js:776:9)
    at instantiateModule (http://localhost:3000/_next/static/chunks/_fdad9c64._.js:730:9)
    at getOrInstantiateModuleFromParent (http://localhost:3000/_next/static/chunks/_fdad9c64._.js:658:12)
    at commonJsRequire (http://localhost:3000/_next/static/chunks/_fdad9c64._.js:158:20)
    at requireModule (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:16764:86)
    at initializeModuleChunk (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:17312:25)
    at resolveModuleChunk (http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:17281:43)
    at http://localhost:3000/_next/static/chunks/node_modules_next_dist_compiled_2ce9398a._.js:17655:24

---

_**Assistant**_

This error:

> TypeError: Cannot read properties of undefined (reading 'list') at structure...

means that the function or object you are calling `.list()` on is undefined. In the context of a custom Sanity structure, this almost always means that the `S` (StructureBuilder) argument is missing or not passed correctly.

### Why this happens

In Sanity v3, a custom structure should be a function that receives the StructureBuilder (`S`) argument.  
But when you use the plugin like this:

```ts
structure({structure: myStructure})
```

your `myStructure` should be a function that takes `S` as an argument, like:

```ts
export const structure = (S) => S.list()...
```

If you instead export an object or call the function without passing `S`, `S` will be undefined, and calling `S.list()` will throw this error.

---

## How to Fix

### 1. Check your custom structure export

Open your `./sanity/structure.ts` file and make sure it looks like this:

```ts
// ./sanity/structure.ts
export const structure = (S) =>
  S.list()
    .title('Content')
    .items([
      // ...your custom items here
    ])
```

### 2. Do NOT call your structure function directly

In your `sanity.config.ts`, you should NOT do this:

```ts
structure({structure: myStructure()}) // ❌ This will break!
```

You should do this:

```ts
structure({structure: myStructure}) // ✅ Pass the function, not the result
```

---

## Summary Table

| What you should do                | What you should NOT do         |
|-----------------------------------|-------------------------------|
| `structure({structure: myStructure})` | `structure({structure: myStructure()})` |
| `export const structure = (S) => ...` | `export const structure = ...` (object) |

---

## If you want to use the default structure

Just use:
```ts
structure()
```
and remove any custom structure import.

---

If you want, I can check your `./sanity/structure.ts` file to make sure it's correct. Would you like me to do that?

---

_**User**_

this is the content of structure.ts: import type {StructureResolver} from 'sanity/structure'

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title('Content')
    .items(S.documentTypeListItems())


---

_**Assistant**_

Thank you for sharing your `structure.ts` content.  
Your file is correct and follows the Sanity v3+ pattern:

```ts
import type {StructureResolver} from 'sanity/structure'

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title('Content')
    .items(S.documentTypeListItems())
```

This is the right way to define a custom structure.

---

## How to Use It in `sanity.config.ts`

You should import and use it like this:

```ts
import {structure} from 'sanity/structure'
import {structure as myStructure} from './sanity/structure'

export default defineConfig({
  // ...other config...
  plugins: [
    structure({structure: myStructure}),
    visionTool({defaultApiVersion: apiVersion}),
  ],
})
```

**Key points:**
- Do NOT call `myStructure()` yourself.
- Pass the function as `structure: myStructure`.

---

## If You Still Get the Error

If you are still getting the error about `Cannot read properties of undefined (reading 'list')`, double-check:
1. You are not calling `myStructure()` anywhere.
2. You are not importing the wrong `structure` in your config (make sure you are not accidentally importing from the wrong file).
3. You are using compatible versions of Sanity packages (v3+).

---

## Troubleshooting Steps

- Make sure your `sanity/structure.ts` only exports the function as shown.
- In your `sanity.config.ts`, the relevant part should look like this:

```ts
import {structure} from 'sanity/structure'
import {structure as myStructure} from './sanity/structure'

export default defineConfig({
  // ...other config...
  plugins: [
    structure({structure: myStructure}),
    // ...other plugins
  ],
})
```

---

If you follow this pattern and still get the error, let me know and I can help you debug further (for example, by checking your Sanity package versions or your import paths).  
Would you like me to check your package versions or anything else?

---

_**User**_

update this accordingly: 

---

_**Assistant**_

<details><summary>Edit file: sanity.config.ts</summary>

Update the Sanity config to use the custom structure exported from './sanity/structure' by passing it as the structure option to the structure plugin. Import structure from both 'sanity/structure' and './sanity/structure' (aliased as myStructure). Use structure({structure: myStructure}) in the plugins array instead of structure().

**Chunk 1**
Lines added: 1, lines removed: 1

```diff
@@ -12,5 +12,5 @@
  import {apiVersion, dataset, projectId} from './sanity/env'
  import {schema} from './sanity/schemaTypes'
- import {structure} from './sanity/structure'
+ import {structure as myStructure} from './sanity/structure'
  
  export default defineConfig({
```

**Chunk 2**
Lines added: 1, lines removed: 1

```diff
@@ -21,5 +21,5 @@
    schema,
    plugins: [
-     structure(), 
+     structure({structure: myStructure}), 
      // structureTool({structure}),
      // Vision is for querying with GROQ from inside the Studio
```

</details>

---

<details><summary>Edit file: sanity.config.ts</summary>

Fix the linter error by importing 'structure' from 'sanity/structure' and 'structure as myStructure' from './sanity/structure', then use structure({structure: myStructure}) in the plugins array.

**Chunk 1**
Lines added: 1, lines removed: 1

```diff
@@ -7,5 +7,5 @@
  import {visionTool} from '@sanity/vision'
  import {defineConfig} from 'sanity'
- // import {structureTool} from 'sanity/structure'
+ import {structure} from 'sanity/structure'
  
  // Go to https://www.sanity.io/docs/api-versioning to learn how API versioning works
```

**Chunk 2**
Lines added: undefined, lines removed: 1

```diff
@@ -22,5 +22,4 @@
    plugins: [
      structure({structure: myStructure}), 
-     // structureTool({structure}),
      // Vision is for querying with GROQ from inside the Studio
      // https://www.sanity.io/docs/the-vision-plugin
```

</details>

---

Your `sanity.config.ts` is now correctly set up to use your custom structure. Here’s what was changed:

- Imported both `structure` from `'sanity/structure'` and your custom structure as `myStructure` from `'./sanity/structure'`.
- Used `structure({structure: myStructure})` in the plugins array.

You should now be able to use your custom desk structure without the previous errors. If you encounter any further issues, let me know!

---

