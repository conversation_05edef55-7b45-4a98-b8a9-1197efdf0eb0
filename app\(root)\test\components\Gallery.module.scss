.gallery {
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  height: 800px;
}

.grid1 {
  display: grid;
  height: 800px;
}

.grid1 img {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 0.5rem;
}

.grid2 {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-auto-rows: minmax(150px, auto);
  gap: 0.5rem;
  height: 800px;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 0.5rem;
}
