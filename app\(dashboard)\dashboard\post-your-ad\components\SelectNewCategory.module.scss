@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: rem(24);
  text-align: center;
  width: 100%;
  
  .title {
    color: $danger;
    font-size: $h4;
    font-weight: 500;
    margin-bottom: rem(24);
  }
  
  .description {
    margin-bottom: rem(48);
    max-width: rem(600);
  }
}

.buttonContainer {
  display: flex;
  flex-direction: column;
  gap: rem(16);
  width: 100%;
  max-width: rem(400);
  
  @include only(largeDesktop) {
    flex-direction: row;
    max-width: rem(600);
    justify-content: center;
  }
  @include only(smallDesktop) {
    flex-direction: row;
    max-width: rem(600);
    justify-content: center;
  }
  @include only(tablet) {
    flex-direction: row;
    max-width: rem(600);
    justify-content: center;
  }
}
