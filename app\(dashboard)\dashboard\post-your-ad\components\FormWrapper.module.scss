@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: rem(311);
  @include only(largeDesktop) {
    width: rem(1098);
    margin-left: rem(-48);
  }
  @include only(smallDesktop) {
    width: rem(1000);
  }
  @include only(tablet) {
    width: rem(1000);
  }
  .form {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: rem(311);

    @include only(largeDesktop) {
      width: rem(1098);
      margin-left: rem(-48);
    }

    @include only(smallDesktop) {
      width: rem(1098);
      margin-left: rem(-48);
    }
    @include only(tablet) {
      width: rem(1098);
      margin-left: rem(-48);
    }
    .children {
      display: flex;
      justify-content: center;
      align-items: center;
      height: fit-content;
      height: 100%;
      margin-bottom: rem(64);
    }

    .buttons {
      display: flex;
      flex-direction: column;
      margin-bottom: rem(120);
      .proceedButton {
        margin-bottom: rem(20);
      }
    }
  }

  .progressBar {
    position: fixed;
    bottom: 0;
    z-index: 5;
  }
}
