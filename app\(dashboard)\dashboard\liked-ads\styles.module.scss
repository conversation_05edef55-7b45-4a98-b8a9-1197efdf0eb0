@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
    position: fixed;
    font-size: $h4;
    font-weight: 500;
    color: $danger;
    top: rem(72);
    z-index: 10;
    @include only(largeDesktop) {
      font-size: $h4;
      top: rem(72);
      margin-bottom: rem(48);
    }
    @include only(smallDesktop) {
      font-size: $h4;
      top: rem(72);
      margin-bottom: rem(48);
    }
    @include only(tablet) {
      font-size: $h4;
      top: rem(72);
      margin-bottom: rem(48);
    }
  }

  .listingsContainer {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: rem(954);
    margin-top: rem(48);
  }
}
