@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;

.container {
  display: flex;
  justify-content: center;
  width: rem(800);
  overflow: hidden;
  @include only(largeDesktop) {
    width: rem(1556);
    height: rem(570);
  }

  .gallery {
    display: flex;
    gap: rem(20);
    border-radius: rem(40);
    width: max-content; /* Allow gallery to size based on children without stretching */

    .image {
      border-radius: rem(40);
    }

    .firstImage {
      position: relative;
      border-radius: rem(40);
      flex: 0 0 auto; /* Don't grow or shrink */
      @include only(largeDesktop) {
        width: rem(768); /* Default for landscape */
        &.portrait {
          width: rem(372); /* Width for portrait */
        }
      }
    }

    .remainingImages {
      position: relative;
      column-width: rem(370) !important; /* Keep fixed column width */
      column-gap: rem(20);
      column-fill: auto;
      overflow: hidden;
      border-radius: rem(40);
      max-width: rem(768);
      flex: 0 0 auto; /* Don't grow or shrink; base width on content */
      height: auto; /* Default for small screens */

      @include only(largeDesktop) {
        height: rem(570); /* Fixed height for sequential filling */
      }

      .imageContainer {
        position: relative;
        width: rem(372);
        break-inside: avoid;
        margin-bottom: rem(20);
        border-radius: rem(40);
        // Default height for landscape images
        height: rem(275);

        &[data-orientation="portrait"] {
          height: rem(570);
        }
      }

      .buttonContainer {
        position: absolute;
        bottom: 110;
        right: 102;
        z-index: 10;
      }
    }
  }
}
