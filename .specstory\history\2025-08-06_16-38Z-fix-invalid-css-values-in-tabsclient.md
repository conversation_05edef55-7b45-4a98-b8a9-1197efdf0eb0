<!-- Generated by SpecStory -->

# Fix invalid CSS values in TabsClient (2025-08-06 16:38Z)

_**User**_

In components/TabsClient.tsx around lines 131 to 133 and also lines 143 to 147, the current use of the short-circuit pattern with collageViewWidth can produce invalid CSS values like "undefinedrem" or "0rem". To fix this, replace the short-circuit check with an explicit conditional that verifies collageViewWidth is a positive number; if not, fallback to using width or another valid default value for the calculation. This ensures the generated rem values are always valid and prevents layout breakage.

---

_**Assistant**_



---

