@use "@/utils/functions" as *;
@use "@/utils/variables" as *;
@use "@/utils/breakpoints" as *;
.cardContainer {
  position: relative;
  display: flex;
  flex-direction: column;
  text-align: center;
  border-radius: rem(40);
  cursor: pointer;
  box-shadow: $shadow-one;
  background-color: $grey-one;
  .imageContainer {
    height: rem(128);
    border-radius: rem(40) rem(40) 0 0;
    position: absolute;

    @include only(largeDesktop) {
      height: rem(216);
    }
    @include only(smallDesktop) {
      height: rem(216);
    }
  }
  .largeLabelContainer {
    position: absolute;
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 0 0 rem(40) rem(40);
    margin-top: auto;
    background-color: $grey-one;

    .label {
      color: $black-four;
      letter-spacing: 1.15px !important;
      text-align: center;
      font-size: rem(20);
      font-weight: 500;
      line-height: 1.2;
      padding: 0 rem(20);
    }
  }
}
.standardLabelContainer {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 0 0 rem(40) rem(40);
  margin-top: auto;
  background-color: $grey-one;
}

.large {
  position: relative;
  width: rem(306);
  height: rem(338);
}

.standard {
  position: relative;
  width: rem(228) !important;
  height: rem(236);
}

.largeImageContainer {
  border-radius: rem(40) rem(40) 0 0;
  height: rem(307);
}
.largeLabelContainer {
  height: rem(88);
  border-radius: 0 0 rem(40) rem(40);
  display: flex;
  justify-content: center;
  align-items: center;

  .label {
    font-size: $h3;
  }
}

.standardLabelContainer {
  height: rem(56);
  font-size: $h4;
  padding: rem(8) !important;
  width: rem(228);

  @include only(largeDesktop) {
    height: rem(72);
  }
  @include only(smallDesktop) {
    height: rem(72);
  }
}

.cardContainer:hover {
  transform: scale(1.04);
  // border-radius: rem(40);
}
.cardContainer:hover .labelContainer {
  // background-color: $white-three;
  // color: $white-two;
}
