@use "@/utils/variables" as *;
@use "@/utils/functions" as *;
@use "@/utils/breakpoints" as *;
.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;

  @include only(largeDesktop) {
    width: rem(1098);
  }
  @include only(smallDesktop) {
    width: rem(960);
  }
  @include only(tablet) {
    width: rem(960);
  }

  .title {
    margin-top: rem(240);
    margin-bottom: rem(48);
    color: $danger;
    font-weight: 500;
    font-size: $h4;
  }

  .mainSection {
    position: relative;
    margin-bottom: rem(20);
    height: 100%;

    .controls {
      margin-bottom: rem(20);
    }
    .priceContainer {
    }

    .startTimeContainer {
      height: fit-content;

      .startTime {
      }
    }
  }
}
