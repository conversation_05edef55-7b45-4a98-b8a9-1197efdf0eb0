import { defineType, defineField } from 'sanity';

export const olyAutoHomepage = defineType({
  name: 'olyAutoHomepage',
  title: 'Oly Auto Homepage',
  type: 'document',
  fields: [
    defineField({
      name: 'title',
      title: 'Oly Homepage Configuration Title',
      type: 'string',
      initialValue: 'Oly Auto Homepage Configuration',
      description: 'Internal title for this auto homepage configuration.',
    }),

    defineField({
      name: 'isActive',
      title: 'Is Active',
      type: 'boolean',
      initialValue: true,
      description: 'Whether this auto homepage configuration is currently active.',
    }),

    defineField({
      name: 'heroSection',
      title: 'Hero Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Hero Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the hero section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Auto Hero Section',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Hero Section Reference',
          type: 'reference',
          to: [{ type: 'heroSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the hero section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 1,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Hero section configuration and reference.',
    }),

    defineField({
      name: 'moreFromOlySection',
      title: 'More from Oly Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable More from Oly Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the More from Oly section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'More from Oly Auto',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'More from Oly Section Reference',
          type: 'reference',
          to: [{ type: 'moreFromOlySection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the More from Oly section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 2,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'More from Oly section configuration and reference.',
    }),

    defineField({
      name: 'featuredServicesSection',
      title: 'Featured Services Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Services Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Services section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Auto Services',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Services Section Reference',
          type: 'reference',
          to: [{ type: 'featuredServicesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Services section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 3,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Featured Services section configuration and reference.',
    }),

    defineField({
      name: 'featuredCategoriesSection',
      title: 'Featured Categories Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Categories Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Categories section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Auto Categories',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Categories Section Reference',
          type: 'reference',
          to: [{ type: 'featuredCategoriesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Categories section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 4,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Featured Categories section configuration and reference.',
    }),

    defineField({
      name: 'featuredVehiclesSection',
      title: 'Featured Vehicles Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Featured Vehicles Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Featured Vehicles section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Vehicles',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Featured Vehicles Section Reference',
          type: 'reference',
          to: [{ type: 'featuredVehiclesSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Featured Vehicles section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 5,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Featured Vehicles section configuration and reference.',
    }),

    defineField({
      name: 'autoCalculatorsSection',
      title: 'Auto Calculators Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Auto Calculators Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Auto Calculators section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Auto Calculators & Tools',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Auto Calculators Section Reference',
          type: 'reference',
          to: [{ type: 'autoCalculatorsSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Auto Calculators section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 6,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Auto Calculators section configuration and reference.',
    }),

    defineField({
      name: 'dealershipsSection',
      title: 'Dealerships Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Dealerships Section',
          type: 'boolean',
          initialValue: true,
          description: 'Display the Dealerships section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Featured Dealerships',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Dealerships Section Reference',
          type: 'reference',
          to: [{ type: 'dealershipsSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Dealerships section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 7,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Dealerships section configuration and reference.',
    }),

    defineField({
      name: 'adSection',
      title: 'Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Ad section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Auto Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Ad Section Reference',
          type: 'reference',
          to: [{ type: 'adSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 8,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Ad section configuration and reference.',
    }),

    defineField({
      name: 'topAdSection',
      title: 'Top Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Top Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Top Ad section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Top Auto Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Top Ad Section Reference',
          type: 'reference',
          to: [{ type: 'topAdSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Top Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 0,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Top Ad section configuration and reference.',
    }),

    defineField({
      name: 'bottomAdSection',
      title: 'Bottom Ad Section',
      type: 'object',
      fields: [
        defineField({
          name: 'isEnabled',
          title: 'Enable Bottom Ad Section',
          type: 'boolean',
          initialValue: false,
          description: 'Display the Bottom Ad section on the auto homepage.',
        }),
        defineField({
          name: 'sectionTitle',
          title: 'Section Title',
          type: 'string',
          initialValue: 'Bottom Auto Advertisement',
          description: 'Display title for this section.',
        }),
        defineField({
          name: 'reference',
          title: 'Bottom Ad Section Reference',
          type: 'reference',
          to: [{ type: 'bottomAdSection' }],
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Reference to the Bottom Ad section configuration.',
        }),
        defineField({
          name: 'sortOrder',
          title: 'Display Order',
          type: 'number',
          initialValue: 9,
          hidden: ({ parent }) => !parent?.isEnabled,
          description: 'Order in which this section appears on the auto homepage.',
        }),
      ],
      description: 'Bottom Ad section configuration and reference.',
    }),

    defineField({
      name: 'autoSettings',
      title: 'Auto-Specific Settings',
      type: 'object',
      fields: [
        defineField({
          name: 'defaultVehicleType',
          title: 'Default Vehicle Type',
          type: 'string',
          options: {
            list: [
              { title: 'All Vehicles', value: 'all' },
              { title: 'Cars', value: 'cars' },
              { title: 'SUVs', value: 'suvs' },
              { title: 'Trucks', value: 'trucks' },
              { title: 'Motorcycles', value: 'motorcycles' },
              { title: 'Commercial Vehicles', value: 'commercial' },
            ],
          },
          initialValue: 'all',
          description: 'Default vehicle type filter for the homepage.',
        }),
        defineField({
          name: 'showPriceRanges',
          title: 'Show Price Ranges',
          type: 'boolean',
          initialValue: true,
          description: 'Display price range filters on the auto homepage.',
        }),
        defineField({
          name: 'showMakeModelFilters',
          title: 'Show Make/Model Filters',
          type: 'boolean',
          initialValue: true,
          description: 'Display make and model filters on the auto homepage.',
        }),
        defineField({
          name: 'showYearFilters',
          title: 'Show Year Filters',
          type: 'boolean',
          initialValue: true,
          description: 'Display year range filters on the auto homepage.',
        }),
        defineField({
          name: 'enableFinancingCalculator',
          title: 'Enable Financing Calculator',
          type: 'boolean',
          initialValue: true,
          description: 'Enable auto financing calculator integration.',
        }),
        defineField({
          name: 'enableTradeInEstimator',
          title: 'Enable Trade-In Estimator',
          type: 'boolean',
          initialValue: true,
          description: 'Enable trade-in value estimation tools.',
        }),
        defineField({
          name: 'showDealershipLocator',
          title: 'Show Dealership Locator',
          type: 'boolean',
          initialValue: true,
          description: 'Display dealership location finder on the homepage.',
        }),
      ],
      description: 'Settings specific to the auto homepage.',
    }),

    defineField({
      name: 'seoSettings',
      title: 'SEO Settings',
      type: 'object',
      fields: [
        defineField({
          name: 'metaTitle',
          title: 'Meta Title',
          type: 'string',
          validation: (Rule) => Rule.max(60),
          description: 'SEO title for the auto homepage (max 60 characters).',
        }),
        defineField({
          name: 'metaDescription',
          title: 'Meta Description',
          type: 'text',
          rows: 3,
          validation: (Rule) => Rule.max(160),
          description: 'SEO description for the auto homepage (max 160 characters).',
        }),
        defineField({
          name: 'keywords',
          title: 'Keywords',
          type: 'array',
          of: [{ type: 'string' }],
          options: {
            layout: 'tags',
          },
          description: 'SEO keywords for the auto homepage.',
        }),
      ],
      description: 'SEO settings for the auto homepage.',
    }),

    defineField({
      name: 'publishedAt',
      title: 'Published At',
      type: 'datetime',
      initialValue: new Date().toISOString(),
      description: 'When this auto homepage configuration was published.',
    }),
  ],

  preview: {
    select: {
      title: 'title',
      isActive: 'isActive',
      heroEnabled: 'heroSection.isEnabled',
      moreFromOlyEnabled: 'moreFromOlySection.isEnabled',
      featuredServicesEnabled: 'featuredServicesSection.isEnabled',
      featuredCategoriesEnabled: 'featuredCategoriesSection.isEnabled',
      featuredVehiclesEnabled: 'featuredVehiclesSection.isEnabled',
      calculatorsEnabled: 'autoCalculatorsSection.isEnabled',
      dealershipsEnabled: 'dealershipsSection.isEnabled',
      defaultVehicleType: 'autoSettings.defaultVehicleType',
    },
    prepare(selection) {
      const { 
        title, 
        isActive, 
        heroEnabled, 
        moreFromOlyEnabled, 
        featuredServicesEnabled, 
        featuredCategoriesEnabled, 
        featuredVehiclesEnabled,
        calculatorsEnabled,
        dealershipsEnabled,
        defaultVehicleType 
      } = selection;
      
      const enabledSections = [
        heroEnabled,
        moreFromOlyEnabled,
        featuredServicesEnabled,
        featuredCategoriesEnabled,
        featuredVehiclesEnabled,
        calculatorsEnabled,
        dealershipsEnabled,
      ].filter(Boolean).length;
      
      return {
        title: title || 'Auto Homepage Configuration',
        subtitle: `${isActive ? '✅ Active' : '❌ Inactive'} - ${enabledSections} sections enabled (${defaultVehicleType || 'all'} vehicles)${calculatorsEnabled ? ' + Tools' : ''}`,
        media: '🚗',
      };
    },
  },
});